package com.jinghang.cash.api;

import com.jinghang.cash.annotation.AnonymousAccess;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.service.ProjectAgreementService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目协议ApiController
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 14:00
 */
@RestController
@RequestMapping("/api/projectAgreement")
public class ProjectAgreementApiController implements ProjectAgreementApiService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAgreementApiController.class);

    @Autowired
    private ProjectAgreementService projectAgreementService;

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    @AnonymousAccess
    @Override
    public ProjectAgreementDto getByStageAndType(@RequestParam("projectCode") String projectCode,
                                                 @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
                                                 @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage,
                                                 @RequestParam("contractTemplateType") String contractTemplateType) {
        logger.info("接收到查询项目协议请求，projectCode: {}, flowLoanStage: {}, capitalLoanStage: {}, contractTemplateType: {}",
                projectCode, flowLoanStage, capitalLoanStage, contractTemplateType);

        try {
            // 参数校验
            if (StringUtils.isBlank(projectCode) || StringUtils.isBlank(contractTemplateType)) {
                logger.info("项目编码或合同模板类型为空");
                return null;
            }

            // 调用业务服务
            ProjectAgreement projectAgreement = projectAgreementService.getByStageAndType(
                    projectCode.trim(), flowLoanStage, capitalLoanStage, contractTemplateType.trim());

            if (projectAgreement == null) {
                logger.info("未找到项目协议，projectCode: {}, contractTemplateType: {}", projectCode, contractTemplateType);
                return null;
            }

            // 转换为DTO
            ProjectAgreementDto dto = convertToDto(projectAgreement);
            logger.info("查询项目协议成功，projectCode: {}", projectCode);
            return dto;

        } catch (Exception e) {
            logger.error("查询项目协议异常，projectCode: {}, contractTemplateType: {}", projectCode, contractTemplateType, e);
            return null;
        }
    }

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param isReturnToFlow 是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    @AnonymousAccess
    @Override
    public List<ProjectAgreementDto> getByReturnStatus(@RequestParam("projectCode") String projectCode,
                                                       @RequestParam(value = "isReturnToFlow", required = false) String isReturnToFlow,
                                                       @RequestParam(value = "isReturnToCapital", required = false) String isReturnToCapital) {
        logger.info("接收到根据退回状态查询项目协议列表请求，projectCode: {}, isReturnToFlow: {}, isReturnToCapital: {}",
                projectCode, isReturnToFlow, isReturnToCapital);

        try {
            // 参数校验
            if (StringUtils.isBlank(projectCode)) {
                logger.info("项目编码为空");
                return new ArrayList<>();
            }

            // 转换枚举参数
            ActiveInactive returnToFlow = StringUtils.isNotBlank(isReturnToFlow) ?
                    ActiveInactive.valueOf(isReturnToFlow) : null;
            ActiveInactive returnToCapital = StringUtils.isNotBlank(isReturnToCapital) ?
                    ActiveInactive.valueOf(isReturnToCapital) : null;

            // 调用业务服务
            List<ProjectAgreement> projectAgreements = projectAgreementService.getByReturnStatus(
                    projectCode.trim(), returnToFlow, returnToCapital);

            // 转换为DTO列表
            List<ProjectAgreementDto> dtoList = convertToDtoList(projectAgreements);
            logger.info("查询项目协议列表成功，projectCode: {}, 返回数量: {}", projectCode, dtoList.size());
            return dtoList;

        } catch (Exception e) {
            logger.error("根据退回状态查询项目协议列表异常，projectCode: {}", projectCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    @AnonymousAccess
    @Override
    public List<ProjectAgreementDto> getByStage(@RequestParam("projectCode") String projectCode,
                                                @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
                                                @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage) {
        logger.info("接收到根据阶段查询项目协议列表请求，projectCode: {}, flowLoanStage: {}, capitalLoanStage: {}",
                projectCode, flowLoanStage, capitalLoanStage);

        try {
            // 参数校验
            if (StringUtils.isBlank(projectCode)) {
                logger.info("项目编码为空");
                return new ArrayList<>();
            }

            // 调用业务服务
            List<ProjectAgreement> projectAgreements = projectAgreementService.getByStage(
                    projectCode.trim(), flowLoanStage, capitalLoanStage);

            // 转换为DTO列表
            List<ProjectAgreementDto> dtoList = convertToDtoList(projectAgreements);
            logger.info("查询项目协议列表成功，projectCode: {}, 返回数量: {}", projectCode, dtoList.size());
            return dtoList;

        } catch (Exception e) {
            logger.error("根据阶段查询项目协议列表异常，projectCode: {}", projectCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将ProjectAgreement转换为ProjectAgreementDto
     *
     * @param projectAgreement 项目协议实体
     * @return 项目协议DTO
     */
    private ProjectAgreementDto convertToDto(ProjectAgreement projectAgreement) {
        if (projectAgreement == null) {
            return null;
        }

        ProjectAgreementDto dto = new ProjectAgreementDto();
        BeanUtils.copyProperties(projectAgreement, dto);

        return dto;
    }

    /**
     * 将ProjectAgreement列表转换为ProjectAgreementDto列表
     *
     * @param projectAgreements 项目协议实体列表
     * @return 项目协议DTO列表
     */
    private List<ProjectAgreementDto> convertToDtoList(List<ProjectAgreement> projectAgreements) {
        if (projectAgreements == null || projectAgreements.isEmpty()) {
            return new ArrayList<>();
        }

        List<ProjectAgreementDto> dtoList = new ArrayList<>();
        for (ProjectAgreement projectAgreement : projectAgreements) {
            ProjectAgreementDto dto = convertToDto(projectAgreement);
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        return dtoList;
    }
}
